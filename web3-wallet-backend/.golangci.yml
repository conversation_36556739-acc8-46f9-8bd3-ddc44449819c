run:
  timeout: 5m
  issues-exit-code: 1
  tests: true
  skip-dirs:
    - vendor
    - api/proto
    - docs
  skip-files:
    - ".*\\.pb\\.go$"
    - ".*_test\\.go$"

output:
  format: colored-line-number
  print-issued-lines: true
  print-linter-name: true

linters-settings:
  errcheck:
    check-type-assertions: true
    check-blank: true
  
  govet:
    check-shadowing: true
    enable-all: true
  
  gocyclo:
    min-complexity: 15
  
  maligned:
    suggest-new: true
  
  dupl:
    threshold: 100
  
  goconst:
    min-len: 3
    min-occurrences: 3
  
  misspell:
    locale: US
  
  lll:
    line-length: 120
  
  goimports:
    local-prefixes: github.com/DimaJoyti/go-coffee
  
  gocritic:
    enabled-tags:
      - performance
      - style
      - experimental
    disabled-checks:
      - wrapperFunc
      - dupImport
  
  funlen:
    lines: 100
    statements: 50
  
  gocognit:
    min-complexity: 20
  
  nestif:
    min-complexity: 4
  
  prealloc:
    simple: true
    range-loops: true
    for-loops: false
  
  nolintlint:
    allow-leading-space: true
    allow-unused: false
    require-explanation: false
    require-specific: false

linters:
  enable:
    - bodyclose
    - deadcode
    - depguard
    - dogsled
    - dupl
    - errcheck
    - exportloopref
    - exhaustive
    - funlen
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - gomnd
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - interfacer
    - lll
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - rowserrcheck
    - scopelint
    - staticcheck
    - structcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - varcheck
    - whitespace
  
  disable:
    - maligned
    - prealloc
    - gochecknoglobals
    - gocognit
    - nestif

issues:
  exclude-rules:
    # Exclude some linters from running on tests files
    - path: _test\.go
      linters:
        - gocyclo
        - errcheck
        - dupl
        - gosec
        - funlen
    
    # Exclude some staticcheck messages
    - linters:
        - staticcheck
      text: "SA9003:"
    
    # Exclude lll issues for long lines with go:generate
    - linters:
        - lll
      source: "^//go:generate "
    
    # Exclude gosec for test files
    - path: _test\.go
      linters:
        - gosec
    
    # Exclude some issues in generated files
    - path: \.pb\.go
      linters:
        - errcheck
        - gosec
        - dupl
        - lll
    
    # Exclude some issues in main files
    - path: main\.go
      linters:
        - gochecknoinits
  
  exclude:
    # errcheck: Almost all programs ignore errors on these functions and in most cases it's ok
    - Error return value of .((os\.)?std(out|err)\..*|.*Close|.*Flush|os\.Remove(All)?|.*printf?|os\.(Un)?Setenv). is not checked
    
    # govet: Common false positives
    - (possible misuse of unsafe.Pointer|should have signature)
    
    # staticcheck: Developers tend to write in C-style with an explicit 'break' in a 'switch', so it's ok to ignore
    - ineffective break statement. Did you mean to break out of the outer loop
    
    # gosec: Too many false-positives on 'unsafe' usage
    - (G103|G104|G204|G301|G302|G304|G401|G501|G502)
    
    # gosec: These only make sense when scoped to a specific domain like authentication
    - G101
    
    # gocritic: These are not always applicable
    - (rangeValCopy|hugeParam)

  max-issues-per-linter: 0
  max-same-issues: 0
  new: false

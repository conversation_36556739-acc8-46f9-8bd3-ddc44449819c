version: '3.8'

services:
  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: web3-coffee-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - web3-coffee-network

  # Ollama for local AI processing
  ollama:
    image: ollama/ollama:latest
    container_name: web3-coffee-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - web3-coffee-network

  # PostgreSQL for persistent data
  postgres:
    image: postgres:15-alpine
    container_name: web3-coffee-postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: web3_coffee
      POSTGRES_USER: web3_user
      POSTGRES_PASSWORD: web3_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U web3_user -d web3_coffee"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - web3-coffee-network

  # Telegram Bot
  telegram-bot:
    build:
      context: ../../
      dockerfile: deployments/telegram-bot/Dockerfile
    container_name: web3-coffee-telegram-bot
    ports:
      - "8087:8087"
    environment:
      # Telegram Configuration
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_WEBHOOK_URL=${TELEGRAM_WEBHOOK_URL}
      
      # AI Configuration
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      
      # Database Configuration
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=web3_coffee
      - DB_USER=web3_user
      - DB_PASSWORD=web3_password
      
      # Redis Configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      
      # Ollama Configuration
      - OLLAMA_HOST=ollama
      - OLLAMA_PORT=11434
      
      # Application Configuration
      - APP_ENV=development
      - LOG_LEVEL=debug
    volumes:
      - ./config:/app/config
      - bot_logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
      ollama:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8087/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - web3-coffee-network

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: web3-coffee-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - web3-coffee-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: web3-coffee-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - web3-coffee-network

volumes:
  redis_data:
    driver: local
  ollama_data:
    driver: local
  postgres_data:
    driver: local
  bot_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  web3-coffee-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

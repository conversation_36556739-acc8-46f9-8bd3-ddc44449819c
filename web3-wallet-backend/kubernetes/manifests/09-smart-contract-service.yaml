apiVersion: apps/v1
kind: Deployment
metadata:
  name: smart-contract-service
  namespace: web3-wallet
spec:
  replicas: 2
  selector:
    matchLabels:
      app: smart-contract-service
  template:
    metadata:
      labels:
        app: smart-contract-service
    spec:
      containers:
      - name: smart-contract-service
        image: yourusername/web3-wallet-smart-contract-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 50053
          name: grpc
        envFrom:
        - configMapRef:
            name: web3-wallet-config
        - secretRef:
            name: web3-wallet-secret
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          exec:
            command:
            - grpc_health_probe
            - -addr=localhost:50053
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        readinessProbe:
          exec:
            command:
            - grpc_health_probe
            - -addr=localhost:50053
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: smart-contract-service
  namespace: web3-wallet
spec:
  selector:
    app: smart-contract-service
  ports:
  - port: 50053
    targetPort: 50053
    name: grpc
  type: ClusterIP

apiVersion: apps/v1
kind: Deployment
metadata:
  name: defi-service
  namespace: web3-wallet
  labels:
    app: defi-service
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: defi-service
  template:
    metadata:
      labels:
        app: defi-service
        version: v1
    spec:
      containers:
      - name: defi-service
        image: web3-wallet/defi-service:latest
        ports:
        - containerPort: 8085
          name: http
        - containerPort: 50055
          name: grpc
        env:
        - name: CONFIG_FILE
          value: "/app/config/config.yaml"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: web3-wallet-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: web3-wallet-secrets
              key: redis-url
        - name: ETHEREUM_RPC_URL
          valueFrom:
            secretKeyRef:
              name: web3-wallet-secrets
              key: ethereum-rpc-url
        - name: BSC_RPC_URL
          valueFrom:
            secretKeyRef:
              name: web3-wallet-secrets
              key: bsc-rpc-url
        - name: P<PERSON>Y<PERSON><PERSON>_RPC_URL
          valueFrom:
            secretKeyRef:
              name: web3-wallet-secrets
              key: polygon-rpc-url
        - name: ONEINCH_API_KEY
          valueFrom:
            secretKeyRef:
              name: web3-wallet-secrets
              key: oneinch-api-key
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: logs-volume
          mountPath: /app/logs
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: config-volume
        configMap:
          name: web3-wallet-config
      - name: logs-volume
        emptyDir: {}
      imagePullSecrets:
      - name: docker-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: defi-service
  namespace: web3-wallet
  labels:
    app: defi-service
spec:
  selector:
    app: defi-service
  ports:
  - name: http
    port: 8085
    targetPort: 8085
    protocol: TCP
  - name: grpc
    port: 50055
    targetPort: 50055
    protocol: TCP
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: defi-service-hpa
  namespace: web3-wallet
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: defi-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: defi-service-ingress
  namespace: web3-wallet
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.go-coffee.com
    secretName: go-coffee-tls
  rules:
  - host: api.go-coffee.com
    http:
      paths:
      - path: /api/v1/defi(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: defi-service
            port:
              number: 8085
---
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: defi-service-monitor
  namespace: web3-wallet
  labels:
    app: defi-service
spec:
  selector:
    matchLabels:
      app: defi-service
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: defi-service-pdb
  namespace: web3-wallet
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: defi-service

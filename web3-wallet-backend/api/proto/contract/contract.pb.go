// Code generated by protoc-gen-go. DO NOT EDIT.
package contract

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

// ContractRequest represents a smart contract interaction request
type ContractRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractAddress string `protobuf:"bytes,1,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	Method          string `protobuf:"bytes,2,opt,name=method,proto3" json:"method,omitempty"`
	Parameters      string `protobuf:"bytes,3,opt,name=parameters,proto3" json:"parameters,omitempty"`
	From            string `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty"`
}

func (x *ContractRequest) Reset() {
	*x = ContractRequest{}
}

func (x *ContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractRequest) ProtoMessage() {}

func (x *ContractRequest) ProtoReflect() protoreflect.Message {
	return nil
}

// ContractResponse represents a smart contract interaction response
type ContractResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionHash string `protobuf:"bytes,1,opt,name=transaction_hash,json=transactionHash,proto3" json:"transaction_hash,omitempty"`
	Result          string `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	Status          string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ContractResponse) Reset() {
	*x = ContractResponse{}
}

func (x *ContractResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractResponse) ProtoMessage() {}

func (x *ContractResponse) ProtoReflect() protoreflect.Message {
	return nil
}

// ContractServiceClient is the client API for ContractService service.
type ContractServiceClient interface {
	ExecuteContract(ctx context.Context, in *ContractRequest, opts ...grpc.CallOption) (*ContractResponse, error)
}

type contractServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewContractServiceClient(cc grpc.ClientConnInterface) ContractServiceClient {
	return &contractServiceClient{cc}
}

func (c *contractServiceClient) ExecuteContract(ctx context.Context, in *ContractRequest, opts ...grpc.CallOption) (*ContractResponse, error) {
	out := new(ContractResponse)
	err := c.cc.Invoke(ctx, "/contract.ContractService/ExecuteContract", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ContractServiceServer is the server API for ContractService service.
type ContractServiceServer interface {
	ExecuteContract(context.Context, *ContractRequest) (*ContractResponse, error)
}

// UnimplementedContractServiceServer can be embedded to have forward compatible implementations.
type UnimplementedContractServiceServer struct {
}

func (*UnimplementedContractServiceServer) ExecuteContract(context.Context, *ContractRequest) (*ContractResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteContract not implemented")
}

func RegisterContractServiceServer(s *grpc.Server, srv ContractServiceServer) {
	s.RegisterService(&_ContractService_serviceDesc, srv)
}

var _ContractService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "contract.ContractService",
	HandlerType: (*ContractServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ExecuteContract",
			Handler:    _ContractService_ExecuteContract_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "contract.proto",
}

func _ContractService_ExecuteContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContractServiceServer).ExecuteContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/contract.ContractService/ExecuteContract",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContractServiceServer).ExecuteContract(ctx, req.(*ContractRequest))
	}
	return interceptor(ctx, info, handler)
}

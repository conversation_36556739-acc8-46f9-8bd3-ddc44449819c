// Code generated by protoc-gen-go. DO NOT EDIT.
package security

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

// SecurityRequest represents a security validation request
type SecurityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Action    string `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	Resource  string `protobuf:"bytes,3,opt,name=resource,proto3" json:"resource,omitempty"`
	Timestamp int64  `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *SecurityRequest) Reset() {
	*x = SecurityRequest{}
}

func (x *SecurityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityRequest) ProtoMessage() {}

func (x *SecurityRequest) ProtoReflect() protoreflect.Message {
	return nil
}

// SecurityResponse represents a security validation response
type SecurityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Allowed bool   `protobuf:"varint,1,opt,name=allowed,proto3" json:"allowed,omitempty"`
	Reason  string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *SecurityResponse) Reset() {
	*x = SecurityResponse{}
}

func (x *SecurityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityResponse) ProtoMessage() {}

func (x *SecurityResponse) ProtoReflect() protoreflect.Message {
	return nil
}

// SecurityServiceClient is the client API for SecurityService service.
type SecurityServiceClient interface {
	ValidateAccess(ctx context.Context, in *SecurityRequest, opts ...grpc.CallOption) (*SecurityResponse, error)
}

type securityServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSecurityServiceClient(cc grpc.ClientConnInterface) SecurityServiceClient {
	return &securityServiceClient{cc}
}

func (c *securityServiceClient) ValidateAccess(ctx context.Context, in *SecurityRequest, opts ...grpc.CallOption) (*SecurityResponse, error) {
	out := new(SecurityResponse)
	err := c.cc.Invoke(ctx, "/security.SecurityService/ValidateAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SecurityServiceServer is the server API for SecurityService service.
type SecurityServiceServer interface {
	ValidateAccess(context.Context, *SecurityRequest) (*SecurityResponse, error)
}

// UnimplementedSecurityServiceServer can be embedded to have forward compatible implementations.
type UnimplementedSecurityServiceServer struct {
}

func (*UnimplementedSecurityServiceServer) ValidateAccess(context.Context, *SecurityRequest) (*SecurityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateAccess not implemented")
}

func RegisterSecurityServiceServer(s *grpc.Server, srv SecurityServiceServer) {
	s.RegisterService(&_SecurityService_serviceDesc, srv)
}

var _SecurityService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "security.SecurityService",
	HandlerType: (*SecurityServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ValidateAccess",
			Handler:    _SecurityService_ValidateAccess_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "security.proto",
}

func _SecurityService_ValidateAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SecurityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServiceServer).ValidateAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.SecurityService/ValidateAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServiceServer).ValidateAccess(ctx, req.(*SecurityRequest))
	}
	return interceptor(ctx, info, handler)
}

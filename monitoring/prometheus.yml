# Prometheus Configuration for Fintech Platform
# This configuration monitors all components of the fintech platform

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'fintech-platform'
    environment: 'development'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 15s

  # Fintech API Server
  - job_name: 'fintech-api'
    static_configs:
      - targets: ['fintech-api:9090']
    metrics_path: /metrics
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: fintech-api:9090

  # Content Analysis Service
  - job_name: 'content-analysis-service'
    static_configs:
      - targets: ['content-analysis-service:9090']
    metrics_path: /metrics
    scrape_interval: 15s

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: /metrics
    scrape_interval: 30s
    # Note: Requires postgres_exporter to be installed

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: /metrics
    scrape_interval: 15s
    # Note: Requires redis_exporter to be installed

  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    metrics_path: /metrics
    scrape_interval: 15s

  # Docker Container Metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    metrics_path: /metrics
    scrape_interval: 15s

  # Nginx Metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    metrics_path: /metrics
    scrape_interval: 15s
    # Note: Requires nginx-prometheus-exporter

  # Custom Application Metrics
  - job_name: 'fintech-custom-metrics'
    static_configs:
      - targets: ['fintech-api:8080']
    metrics_path: /api/v1/metrics
    scrape_interval: 30s
    honor_labels: true

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint.com/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# Remote read configuration
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint.com/api/v1/read"
#     basic_auth:
#       username: "username"
#       password: "password"

# Storage configuration
storage:
  tsdb:
    path: /prometheus
    retention.time: 15d
    retention.size: 10GB
    wal-compression: true

# Web configuration
web:
  console.templates: /etc/prometheus/consoles
  console.libraries: /etc/prometheus/console_libraries
  enable-lifecycle: true
  enable-admin-api: true

# Fintech-Specific Metric Collection Targets

# Key Metrics to Monitor:
# 
# 1. Business Metrics:
#    - Payment success/failure rates
#    - Transaction volumes and values
#    - User registration rates
#    - KYC approval rates
#    - Card transaction success rates
#    - Yield farming returns
#    - Trading order execution rates
#
# 2. Technical Metrics:
#    - API response times
#    - Database query performance
#    - Cache hit/miss rates
#    - Error rates by endpoint
#    - System resource usage
#    - Network latency
#
# 3. Security Metrics:
#    - Failed login attempts
#    - Fraud detection alerts
#    - Suspicious transaction patterns
#    - Rate limiting triggers
#    - Security event frequencies
#
# 4. Infrastructure Metrics:
#    - Container health and resource usage
#    - Database connection pools
#    - Queue lengths and processing times
#    - Disk usage and I/O
#    - Network throughput

# Example Custom Metrics for Fintech Platform:
#
# # Payment Processing Metrics
# fintech_payments_total{status="success|failed|pending", method="crypto|card|bank"}
# fintech_payment_amount_total{currency="USD|EUR|BTC|ETH"}
# fintech_payment_processing_duration_seconds
# fintech_payment_fees_total{currency="USD|EUR|BTC|ETH"}
#
# # Account Management Metrics
# fintech_accounts_total{type="personal|business|enterprise", status="active|inactive|suspended"}
# fintech_kyc_verifications_total{status="approved|rejected|pending", level="basic|standard|enhanced"}
# fintech_login_attempts_total{status="success|failed", method="password|2fa"}
# fintech_sessions_active_total
#
# # Trading Metrics
# fintech_orders_total{type="market|limit|stop", side="buy|sell", status="filled|cancelled|rejected"}
# fintech_trade_volume_total{symbol="BTC/USD|ETH/USD|etc"}
# fintech_portfolio_value_total{currency="USD|EUR|BTC|ETH"}
# fintech_order_execution_duration_seconds
#
# # Yield Farming Metrics
# fintech_yield_positions_total{protocol="uniswap|compound|aave", status="active|closed"}
# fintech_yield_rewards_total{token="UNI|COMP|AAVE", status="claimed|pending"}
# fintech_yield_apy_current{protocol="uniswap|compound|aave"}
# fintech_yield_tvl_total{protocol="uniswap|compound|aave"}
#
# # Card Metrics
# fintech_cards_total{type="virtual|physical", status="active|blocked|expired"}
# fintech_card_transactions_total{type="purchase|withdrawal|refund", status="approved|declined"}
# fintech_card_spending_total{category="dining|grocery|gas|online"}
# fintech_card_fraud_alerts_total{severity="low|medium|high"}
#
# # System Performance Metrics
# fintech_api_requests_total{method="GET|POST|PUT|DELETE", endpoint="/api/v1/...", status="2xx|4xx|5xx"}
# fintech_api_request_duration_seconds{method="GET|POST|PUT|DELETE", endpoint="/api/v1/..."}
# fintech_database_queries_total{operation="select|insert|update|delete", table="accounts|payments|etc"}
# fintech_database_query_duration_seconds{operation="select|insert|update|delete"}
# fintech_cache_operations_total{operation="get|set|delete", status="hit|miss"}
# fintech_queue_jobs_total{queue="payments|notifications|kyc", status="pending|processing|completed|failed"}
#
# # Security Metrics
# fintech_security_events_total{type="login|fraud|suspicious", severity="low|medium|high|critical"}
# fintech_rate_limit_exceeded_total{endpoint="/api/v1/...", user_type="authenticated|anonymous"}
# fintech_failed_authentications_total{method="password|2fa|api_key"}
# fintech_blocked_ips_total{reason="rate_limit|fraud|geo_restriction"}

# Alerting Rules (to be defined in alert_rules.yml):
# - High error rates (>5% for 5 minutes)
# - High response times (>1s p95 for 5 minutes)
# - Database connection issues
# - High memory/CPU usage (>80% for 10 minutes)
# - Failed payment rates (>1% for 5 minutes)
# - Security events (immediate alerts for critical events)
# - Service unavailability
# - Disk space usage (>85%)
# - Queue backlog (>1000 pending jobs)

# Recording Rules (to be defined in recording_rules.yml):
# - Payment success rates by time window
# - Average response times by endpoint
# - User growth rates
# - Revenue metrics
# - System resource utilization trends

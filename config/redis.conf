# Redis Configuration for Fintech Platform
# This configuration is optimized for fintech workloads with focus on
# performance, security, and data persistence

# Network Configuration
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300

# General Configuration
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# Security Configuration
# requirepass your-redis-password-here
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command EVAL ""
# rename-command DEBUG ""
# rename-command CONFIG ""

# Memory Management
maxmemory 2gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Persistence Configuration
# RDB Snapshots
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF (Append Only File)
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Slow Log Configuration
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency Monitoring
latency-monitor-threshold 100

# Client Configuration
maxclients 10000

# Advanced Configuration
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active Rehashing
activerehashing yes

# Client Output Buffer Limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client Query Buffer Limit
client-query-buffer-limit 1gb

# Protocol Buffer Limit
proto-max-bulk-len 512mb

# Frequency of rehashing
hz 10

# Enable dynamic HZ
dynamic-hz yes

# AOF Rewrite Incremental Fsync
aof-rewrite-incremental-fsync yes

# RDB Save Incremental Fsync
rdb-save-incremental-fsync yes

# LFU Configuration (when using allkeys-lfu policy)
# lfu-log-factor 10
# lfu-decay-time 1

# Fintech-Specific Optimizations
# These settings are optimized for typical fintech workloads

# Session Management (TTL for session keys)
# Sessions typically expire in 24 hours
# Use: SET session:user:123 "session_data" EX 86400

# Rate Limiting (for API rate limiting)
# Use sliding window rate limiting
# Use: INCR rate_limit:user:123:minute:**********

# Cache Configuration (for frequently accessed data)
# Account data, exchange rates, etc.
# Use: SET cache:account:123 "account_data" EX 3600

# Real-time Data (for trading, prices)
# Use Redis Streams for real-time price feeds
# Use: XADD price_feed * symbol BTC price 45000

# Pub/Sub for Real-time Notifications
# Use: PUBLISH notifications:user:123 "payment_completed"

# Sorted Sets for Leaderboards/Rankings
# Use: ZADD trading_leaderboard 1000 user:123

# HyperLogLog for Unique Counting
# Use: PFADD unique_visitors:2023-12-01 user:123

# Geospatial for Location-based Features
# Use: GEOADD locations -122.4194 37.7749 "san_francisco"

# Lua Scripts for Atomic Operations
# Use for complex operations like balance transfers

# Module Configuration (if using Redis modules)
# loadmodule /usr/lib/redis/modules/redisearch.so
# loadmodule /usr/lib/redis/modules/redisjson.so
# loadmodule /usr/lib/redis/modules/redistimeseries.so

# Monitoring and Debugging
# Enable keyspace notifications for monitoring
notify-keyspace-events "Ex"

# Disable dangerous commands in production
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command KEYS ""
# rename-command SHUTDOWN SHUTDOWN_FINTECH_PLATFORM

# Memory Usage Optimization
# Use memory-efficient data structures
# Consider using Redis Cluster for horizontal scaling

# Backup and Recovery
# Regular RDB snapshots for point-in-time recovery
# AOF for durability and minimal data loss

# Performance Tuning
# Monitor slow queries and optimize
# Use pipelining for bulk operations
# Consider read replicas for read-heavy workloads

# Security Best Practices
# Use TLS encryption in production
# Implement proper authentication
# Restrict network access
# Regular security updates

# Monitoring Metrics to Track:
# - Memory usage
# - CPU usage
# - Network I/O
# - Slow queries
# - Key expiration rates
# - Client connections
# - Command statistics

# Common Fintech Use Cases:
# 1. Session Storage: User sessions with TTL
# 2. Rate Limiting: API rate limiting with sliding windows
# 3. Caching: Account data, exchange rates, market data
# 4. Real-time Data: Price feeds, trading data
# 5. Pub/Sub: Real-time notifications
# 6. Queues: Background job processing
# 7. Counters: Transaction counts, user activity
# 8. Leaderboards: Trading performance, user rankings
# 9. Geospatial: Location-based services
# 10. Time Series: Historical data, analytics
